<?php

class PFG_MyPos_Helper_Data extends Mage_Core_Helper_Abstract
{
    const LOG_FILE = 'pfg_mypos.log';
    const EXCEPTION_LOG_FILE = 'pfg_mypos_exceptions.log';

    /**
     * Get logger instance
     *
     * @return Zend_Log
     */
    public function getLogger()
    {
        $logFile = Mage::getStoreConfig('pfg_mypos/advanced/log_file_name') ?: self::LOG_FILE;
        return Mage::getModel('core/log_adapter', $logFile);
    }

    /**
     * Get exception logger instance
     *
     * @return Zend_Log
     */
    public function getExceptionLogger()
    {
        $logFile = Mage::getStoreConfig('pfg_mypos/advanced/exceptions_file_name') ?: self::EXCEPTION_LOG_FILE;
        return Mage::getModel('core/log_adapter', $logFile);
    }

    /**
     * Log message
     *
     * @param string $message
     * @param int $level
     */
    public function log($message, $level = Zend_Log::INFO)
    {
        if (Mage::getStoreConfig('pfg_mypos/advanced/force_log') || Mage::getIsDeveloperMode()) {
            $this->getLogger()->log($message, $level);
        }
    }

    /**
     * Log exception
     *
     * @param Exception $exception
     */
    public function logException(Exception $exception)
    {
        $this->getExceptionLogger()->log($exception->getMessage(), Zend_Log::ERR);
        $this->getExceptionLogger()->log($exception->getTraceAsString(), Zend_Log::DEBUG);
    }

    /**
     * Check if sandbox mode is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isSandboxMode($storeId = null)
    {
        return (bool) Mage::getStoreConfig('payment/pfg_mypos/sandbox_mode', $storeId);
    }

    /**
     * Get MyPOS API URL
     *
     * @param int|null $storeId
     * @return string
     */
    public function getApiUrl($storeId = null)
    {
        if ($this->isSandboxMode($storeId)) {
            return Mage::getStoreConfig('pfg_mypos/general/sandbox_url', $storeId);
        }
        return Mage::getStoreConfig('pfg_mypos/general/production_url', $storeId);
    }

    /**
     * Get callback URLs
     *
     * @param int|null $storeId
     * @return array
     */
    public function getCallbackUrls($storeId = null)
    {
        // Get configured URLs from admin panel
        $urlOk = $this->getPaymentConfig('url_ok', $storeId);
        $urlCancel = $this->getPaymentConfig('url_cancel', $storeId);
        $urlNotify = $this->getPaymentConfig('url_notify', $storeId);

        // Fallback to default URLs if not configured
        if (empty($urlOk) || empty($urlCancel) || empty($urlNotify)) {
            $baseUrl = Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_LINK, true);

            if (empty($urlOk)) {
                $urlOk = $baseUrl . 'pfg_mypos/response/success';
            }
            if (empty($urlCancel)) {
                $urlCancel = $baseUrl . 'pfg_mypos/response/cancel';
            }
            if (empty($urlNotify)) {
                $urlNotify = $baseUrl . 'pfg_mypos/response/notify';
            }
        }

        // Validate that URLs are HTTPS as required by MyPOS
        $this->validateHttpsUrl($urlOk, 'Success URL');
        $this->validateHttpsUrl($urlCancel, 'Cancel URL');
        $this->validateHttpsUrl($urlNotify, 'Notify URL');

        return array(
            'success' => $urlOk,
            'cancel' => $urlCancel,
            'notify' => $urlNotify,
        );
    }

    /**
     * Validate that URL starts with https://
     *
     * @param string $url
     * @param string $urlType
     * @throws Mage_Core_Exception
     */
    protected function validateHttpsUrl($url, $urlType)
    {
        if (!preg_match('/^https:\/\//', $url)) {
            Mage::throwException(
                $this->__('%s must start with https:// as required by MyPOS security policy. Current URL: %s', $urlType, $url)
            );
        }
    }

    /**
     * Generate unique order ID for MyPOS
     *
     * @param Mage_Sales_Model_Order $order
     * @return string
     */
    public function generateMyPosOrderId(Mage_Sales_Model_Order $order)
    {
        return $order->getIncrementId() . '_' . time();
    }

    /**
     * Validate MyPOS response signature
     *
     * @param array $responseData
     * @param string $publicKey
     * @return bool
     */
    public function validateSignature(array $responseData, $publicKey)
    {
        // Use the new direct signature validation method
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        return $myposHelper->validateMyPosSignature($responseData, $publicKey);
    }

    /**
     * Format amount for MyPOS (convert to cents)
     *
     * @param float $amount
     * @return int
     */
    public function formatAmount($amount)
    {
        return (int) round($amount * 100);
    }

    /**
     * Format amount from MyPOS (convert from cents)
     *
     * @param int $amount
     * @return float
     */
    public function formatAmountFromMyPos($amount)
    {
        return $amount / 100;
    }

    /**
     * Get order by MyPOS order ID
     *
     * @param string $myposOrderId
     * @return Mage_Sales_Model_Order|null
     */
    public function getOrderByMyPosOrderId($myposOrderId)
    {
        // Extract original increment ID from MyPOS order ID
        $parts = explode('_', $myposOrderId);
        if (count($parts) >= 2) {
            $incrementId = $parts[0];
            return Mage::getModel('sales/order')->loadByIncrementId($incrementId);
        }
        return null;
    }

    /**
     * Get payment method configuration
     *
     * @param string $field
     * @param int|null $storeId
     * @return mixed
     */
    public function getPaymentConfig($field, $storeId = null)
    {
        return Mage::getStoreConfig('payment/pfg_mypos/' . $field, $storeId);
    }

    /**
     * Check if payment method is active
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isActive($storeId = null)
    {
        return (bool) $this->getPaymentConfig('active', $storeId);
    }

    /**
     * Get supported currencies
     *
     * @return array
     */
    public function getSupportedCurrencies()
    {
        return array('BGN', 'EUR', 'USD');
    }

    /**
     * Check if currency is supported
     *
     * @param string $currency
     * @return bool
     */
    public function isCurrencySupported($currency)
    {
        return in_array($currency, $this->getSupportedCurrencies());
    }
}
