<?php

// Include MyPOS SDK
require_once Mage::getModuleDir('', 'PFG_MyPos') . DS . 'lib' . DS . 'mypos-sdk' . DS . 'IPC' . DS . 'Loader.php';

class PFG_MyPos_Helper_MyPos extends Mage_Core_Helper_Abstract
{
    /**
     * Create MyPOS configuration object
     *
     * @param int|null $storeId
     * @return \Mypos\IPC\Config
     */
    public function createConfig($storeId = null)
    {
        $helper = Mage::helper('pfg_mypos');
        $config = new \Mypos\IPC\Config();
        
        // Set basic configuration
        $config->setIpcURL($helper->getApiUrl($storeId));
        $config->setLang('bg');
        $config->setVersion('1.4');
        
        // Check if we have configuration package
        $configPackage = $helper->getPaymentConfig('configuration_package', $storeId);
        if (!empty($configPackage)) {
            $config->loadConfigurationPackage($configPackage);
        } else {
            // Use manual configuration
            $config->setSid($helper->getPaymentConfig('merchant_id', $storeId));
            $config->setWallet($helper->getPaymentConfig('wallet_number', $storeId));
            $config->setKeyIndex((int) $helper->getPaymentConfig('key_index', $storeId) ?: 1);
            
            // Set RSA keys
            $privateKey = $helper->getPaymentConfig('private_key', $storeId);
            $publicKey = $helper->getPaymentConfig('api_public_key', $storeId);
            
            if (!empty($privateKey)) {
                $config->setPrivateKey($privateKey);
            }
            if (!empty($publicKey)) {
                $config->setAPIPublicKey($publicKey);
            }
        }
        
        return $config;
    }

    /**
     * Create customer object from order
     *
     * @param Mage_Sales_Model_Order $order
     * @return \Mypos\IPC\Customer
     */
    public function createCustomerFromOrder(Mage_Sales_Model_Order $order)
    {
        $billingAddress = $order->getBillingAddress();
        $customer = new \Mypos\IPC\Customer();
        
        $customer->setFirstName($billingAddress->getFirstname());
        $customer->setLastName($billingAddress->getLastname());
        $customer->setEmail($order->getCustomerEmail());
        $customer->setPhone($billingAddress->getTelephone());
        
        // Set address information
        $customer->setCountry($billingAddress->getCountryId());
        $customer->setCity($billingAddress->getCity());
        $customer->setZip($billingAddress->getPostcode());
        $customer->setAddress($billingAddress->getStreetFull());
        
        return $customer;
    }

    /**
     * Create cart object from order
     *
     * @param Mage_Sales_Model_Order $order
     * @return \Mypos\IPC\Cart
     */
    public function createCartFromOrder(Mage_Sales_Model_Order $order)
    {
        $cart = new \Mypos\IPC\Cart();
        
        // Add order items
        foreach ($order->getAllVisibleItems() as $item) {
            $cart->add(
                $item->getName(),
                (int) $item->getQtyOrdered(),
                (float) $item->getPrice()
            );
        }
        
        // Add shipping if applicable
        if ($order->getShippingAmount() > 0) {
            $cart->add(
                'Shipping - ' . $order->getShippingDescription(),
                1,
                (float) $order->getShippingAmount()
            );
        }
        
        // Add tax if applicable
        if ($order->getTaxAmount() > 0) {
            $cart->add(
                'Tax',
                1,
                (float) $order->getTaxAmount()
            );
        }
        
        // Add discount if applicable
        if ($order->getDiscountAmount() < 0) {
            $cart->add(
                'Discount',
                1,
                (float) $order->getDiscountAmount()
            );
        }
        
        return $cart;
    }

    /**
     * Create purchase request
     *
     * @param Mage_Sales_Model_Order $order
     * @return \Mypos\IPC\Purchase
     */
    public function createPurchaseRequest(Mage_Sales_Model_Order $order)
    {
        $helper = Mage::helper('pfg_mypos');
        $config = $this->createConfig($order->getStoreId());
        $purchase = new \Mypos\IPC\Purchase($config);
        
        // Set callback URLs
        $urls = $helper->getCallbackUrls($order->getStoreId());
        $purchase->setUrlOk($urls['success']);
        $purchase->setUrlCancel($urls['cancel']);
        $purchase->setUrlNotify($urls['notify']);
        
        // Set order information
        $myposOrderId = $helper->generateMyPosOrderId($order);
        $purchase->setOrderID($myposOrderId);
        $purchase->setCurrency($helper->getPaymentConfig('currency', $order->getStoreId()) ?: 'BGN');
        $purchase->setNote('Order #' . $order->getIncrementId());
        
        // Set customer and cart
        $purchase->setCustomer($this->createCustomerFromOrder($order));
        $purchase->setCart($this->createCartFromOrder($order));
        
        // Set payment parameters
        $purchase->setCardTokenRequest(\Mypos\IPC\Purchase::CARD_TOKEN_REQUEST_NONE);
        $purchase->setPaymentParametersRequired(\Mypos\IPC\Purchase::PURCHASE_TYPE_FULL);
        $purchase->setPaymentMethod(\Mypos\IPC\Purchase::PAYMENT_METHOD_BOTH);
        
        return $purchase;
    }

    /**
     * Process purchase request
     *
     * @param Mage_Sales_Model_Order $order
     * @return array
     */
    public function processPurchase(Mage_Sales_Model_Order $order)
    {
        $helper = Mage::helper('pfg_mypos');

        // Check if we should use direct signing (recommended for better compatibility)
        $useDirectSigning = $helper->getPaymentConfig('use_direct_signing', $order->getStoreId());

        if ($useDirectSigning) {
            // Use the new direct signing method based on successful signing example
            return $this->createDirectPayment($order);
        }

        // Fallback to SDK method for backward compatibility
        try {
            $purchase = $this->createPurchaseRequest($order);

            // Log the request
            $helper->log('Creating MyPOS purchase request for order: ' . $order->getIncrementId());

            // Save transaction record
            $this->saveTransaction($order, $purchase->getOrderID(), 'pending');

            // Process the purchase without auto-submitting the form
            $purchase->process(false);

            // Get form parameters for manual redirect
            $formParams = $purchase->getFormParameters();

            return array(
                'success' => true,
                'mypos_order_id' => $purchase->getOrderID(),
                'redirect_url' => $formParams['ActionUrl'],
                'form_data' => $formParams['FormData']
            );

        } catch (\Mypos\IPC\IPC_Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        } catch (Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error' => 'An unexpected error occurred'
            );
        }
    }

    /**
     * Process refund request
     *
     * @param Mage_Sales_Model_Order $order
     * @param float $amount
     * @param string $transactionId
     * @return array
     */
    public function processRefund(Mage_Sales_Model_Order $order, $amount, $transactionId)
    {
        $helper = Mage::helper('pfg_mypos');
        
        try {
            $config = $this->createConfig($order->getStoreId());
            $refund = new \Mypos\IPC\Refund($config);
            
            $refund->setOrderID($transactionId);
            $refund->setAmount($amount);
            $refund->setCurrency($helper->getPaymentConfig('currency', $order->getStoreId()) ?: 'BGN');
            $refund->setNote('Refund for order #' . $order->getIncrementId());
            
            // Process refund
            $response = $refund->process();
            
            $helper->log('MyPOS refund processed for order: ' . $order->getIncrementId());
            
            return array(
                'success' => true,
                'refund_transaction_id' => $response->getTransactionId()
            );
            
        } catch (\Mypos\IPC\IPC_Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error_message' => $e->getMessage()
            );
        } catch (Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error_message' => 'An unexpected error occurred during refund'
            );
        }
    }

    /**
     * Create MyPOS payment using direct signing (based on successful signing example)
     *
     * @param Mage_Sales_Model_Order $order
     * @return array
     */
    public function createDirectPayment(Mage_Sales_Model_Order $order)
    {
        $helper = Mage::helper('pfg_mypos');

        try {
            // Get configuration
            $config = $this->getDirectPaymentConfig($order->getStoreId());

            // Build payment fields using exact format from successful example
            $fields = $this->buildPaymentFields($order, $config);

            // Create signature using the exact algorithm from successful example
            $signature = $this->createMyPosSignature($fields, $config['private_key']);

            // Add signature to fields
            $fields['Signature'] = $signature;

            // Get endpoint URL
            $endpoint = $config['sandbox_mode'] ?
                'https://www.mypos.eu/vmp/checkout-test' :
                'https://www.mypos.eu/vmp/checkout';

            // Save transaction record
            $myposOrderId = $fields['OrderID'];
            $this->saveTransaction($order, $myposOrderId, 'pending');

            $helper->log('Created MyPOS direct payment for order: ' . $order->getIncrementId());

            return array(
                'success' => true,
                'mypos_order_id' => $myposOrderId,
                'redirect_url' => $endpoint,
                'form_data' => $fields
            );

        } catch (Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Get direct payment configuration
     *
     * @param int|null $storeId
     * @return array
     */
    protected function getDirectPaymentConfig($storeId = null)
    {
        $helper = Mage::helper('pfg_mypos');

        return array(
            'store_id' => $helper->getPaymentConfig('merchant_id', $storeId),
            'wallet_number' => $helper->getPaymentConfig('wallet_number', $storeId),
            'key_index' => (int) $helper->getPaymentConfig('key_index', $storeId) ?: 1,
            'private_key' => $helper->getPaymentConfig('private_key', $storeId),
            'currency' => $helper->getPaymentConfig('currency', $storeId) ?: 'BGN',
            'sandbox_mode' => (bool) $helper->getPaymentConfig('sandbox_mode', $storeId)
        );
    }

    /**
     * Build payment fields using exact format from successful signing example
     *
     * @param Mage_Sales_Model_Order $order
     * @param array $config
     * @return array
     */
    protected function buildPaymentFields(Mage_Sales_Model_Order $order, array $config)
    {
        $helper = Mage::helper('pfg_mypos');
        $urls = $helper->getCallbackUrls($order->getStoreId());

        // Generate unique order ID
        $myposOrderId = $helper->generateMyPosOrderId($order);

        // Get cart items
        $cartItems = $this->getCartItemsFromOrder($order);

        // Build basic fields with exact capitalization from successful example
        $fields = array(
            // Basic payment info - exact field names from successful example
            'IPCmethod' => 'IPCPurchase',
            'IPCVersion' => '1.4',
            'IPCLanguage' => 'EN',
            'SID' => $config['store_id'],
            'walletnumber' => $config['wallet_number'],
            'Amount' => $order->getGrandTotal(), // Capital A as in example
            'Currency' => $config['currency'],    // Capital C as in example
            'OrderID' => $myposOrderId,          // Capital O, I, D as in example

            // URLs - exact field names from example
            'URL_OK' => $urls['success'],
            'URL_Cancel' => $urls['cancel'],
            'URL_Notify' => $urls['notify'], // HTTPS required for notify URL

            // Required fields from example
            'CardTokenRequest' => 0,
            'KeyIndex' => $config['key_index'],
            'PaymentParametersRequired' => 1,

            // Customer details (required when PaymentParametersRequired=1)
            'customeremail' => $order->getCustomerEmail() ?: '<EMAIL>',
            'customerfirstnames' => $order->getBillingAddress()->getFirstname(),
            'customerfamilyname' => $order->getBillingAddress()->getLastname(),
            'customerphone' => $order->getBillingAddress()->getTelephone() ?: '+359888000000',
            'customercountry' => $order->getBillingAddress()->getCountryId() ?: 'BGR',
            'customercity' => $order->getBillingAddress()->getCity() ?: 'Sofia',
            'customerzipcode' => $order->getBillingAddress()->getPostcode() ?: '1000',
            'customeraddress' => $order->getBillingAddress()->getStreetFull() ?: 'Address',

            // Cart info
            'CartItems' => count($cartItems),
        );

        // Add cart item fields exactly as in successful example
        foreach ($cartItems as $index => $item) {
            $itemIndex = $index + 1; // 1-indexed
            $itemAmount = $item['quantity'] * $item['price'];

            $fields["Article_$itemIndex"] = $item['name'];
            $fields["Quantity_$itemIndex"] = $item['quantity'];
            $fields["Price_$itemIndex"] = $item['price'];
            $fields["Currency_$itemIndex"] = $config['currency']; // This was missing in original implementation!
            $fields["Amount_$itemIndex"] = $itemAmount;
        }

        return $fields;
    }

    /**
     * Get cart items from order in the format needed for MyPOS
     *
     * @param Mage_Sales_Model_Order $order
     * @return array
     */
    protected function getCartItemsFromOrder(Mage_Sales_Model_Order $order)
    {
        $cartItems = array();

        // Add order items
        foreach ($order->getAllVisibleItems() as $item) {
            $cartItems[] = array(
                'name' => $item->getName(),
                'quantity' => (int) $item->getQtyOrdered(),
                'price' => (float) $item->getPrice()
            );
        }

        // Add shipping if applicable
        if ($order->getShippingAmount() > 0) {
            $cartItems[] = array(
                'name' => 'Shipping - ' . $order->getShippingDescription(),
                'quantity' => 1,
                'price' => (float) $order->getShippingAmount()
            );
        }

        // Add tax if applicable
        if ($order->getTaxAmount() > 0) {
            $cartItems[] = array(
                'name' => 'Tax',
                'quantity' => 1,
                'price' => (float) $order->getTaxAmount()
            );
        }

        // Add discount if applicable
        if ($order->getDiscountAmount() < 0) {
            $cartItems[] = array(
                'name' => 'Discount',
                'quantity' => 1,
                'price' => (float) $order->getDiscountAmount()
            );
        }

        return $cartItems;
    }

    /**
     * Create MyPOS signature using exact algorithm from successful signing example
     *
     * @param array $fields
     * @param string $privateKey
     * @return string
     */
    protected function createMyPosSignature(array $fields, $privateKey)
    {
        // Sort fields alphabetically - exactly as in successful example
        ksort($fields);

        // Concatenate with dash and Base64 encode - exactly as in successful example
        $dashString = implode('-', $fields);
        $base64String = base64_encode($dashString);

        // Sign with SHA-256 - exactly as in successful example
        $pkeyid = openssl_pkey_get_private($privateKey);
        if ($pkeyid === false) {
            throw new Exception('Failed to load private key for MyPOS signing');
        }

        $signResult = openssl_sign($base64String, $signature, $pkeyid, OPENSSL_ALGO_SHA256);
        openssl_free_key($pkeyid);

        if (!$signResult) {
            throw new Exception('Failed to create MyPOS signature');
        }

        return base64_encode($signature);
    }

    /**
     * Validate MyPOS response signature using exact algorithm from successful signing example
     *
     * @param array $responseData
     * @param string $publicKey
     * @return bool
     */
    public function validateMyPosSignature(array $responseData, $publicKey)
    {
        if (!isset($responseData['Signature'])) {
            return false;
        }

        $signature = $responseData['Signature'];
        unset($responseData['Signature']); // Remove signature from data to validate

        try {
            // Sort fields alphabetically - same as signing process
            ksort($responseData);

            // Concatenate with dash and Base64 encode - same as signing process
            $dashString = implode('-', $responseData);
            $base64String = base64_encode($dashString);

            // Verify signature with SHA-256
            $pubKeyId = openssl_get_publickey($publicKey);
            if ($pubKeyId === false) {
                return false;
            }

            $result = openssl_verify($base64String, base64_decode($signature), $pubKeyId, OPENSSL_ALGO_SHA256);
            openssl_free_key($pubKeyId);

            return $result === 1;

        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            return false;
        }
    }

    /**
     * Save transaction record
     *
     * @param Mage_Sales_Model_Order $order
     * @param string $myposOrderId
     * @param string $status
     * @param array $responseData
     */
    public function saveTransaction(Mage_Sales_Model_Order $order, $myposOrderId, $status, $responseData = array())
    {
        try {
            $transaction = Mage::getModel('pfg_mypos/transaction');
            $transaction->setData(array(
                'order_id' => $order->getId(),
                'mypos_order_id' => $myposOrderId,
                'status' => $status,
                'amount' => $order->getGrandTotal(),
                'currency' => $order->getOrderCurrencyCode(),
                'request_data' => json_encode(array('order_id' => $myposOrderId)),
                'response_data' => json_encode($responseData),
                'created_at' => now(),
                'updated_at' => now()
            ));
            $transaction->save();
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
        }
    }
}
