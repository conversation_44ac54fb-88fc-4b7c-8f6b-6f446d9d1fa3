<?php

class PFG_MyPos_RequestController extends Mage_Core_Controller_Front_Action
{
    /**
     * Redirect to MyPOS payment page
     */
    public function redirectAction()
    {
        try {
            $session = Mage::getSingleton('checkout/session');
            $order = Mage::getModel('sales/order')->loadByIncrementId($session->getLastRealOrderId());
            
            if (!$order->getId()) {
                Mage::getSingleton('core/session')->addError($this->__('Order not found.'));
                $this->_redirect('checkout/cart');
                return;
            }
            
            // Check if payment method is MyPOS
            if ($order->getPayment()->getMethodInstance()->getCode() !== PFG_MyPos_Model_Payment_MyPos::PAYMENT_METHOD_CODE) {
                Mage::getSingleton('core/session')->addError($this->__('Invalid payment method.'));
                $this->_redirect('checkout/cart');
                return;
            }
            
            $helper = Mage::helper('pfg_mypos/mypos');
            $result = $helper->processPurchase($order);

            if (!$result['success']) {
                Mage::getSingleton('core/session')->addError($this->__('Payment processing failed: %s', $result['error']));
                $this->_redirect('checkout/cart');
                return;
            }

            // Create and auto-submit form to redirect to MyPOS
            $this->_createRedirectForm($result['redirect_url'], $result['form_data']);
            return;
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            Mage::getSingleton('core/session')->addError($this->__('An error occurred during payment processing.'));
            $this->_redirect('checkout/cart');
        }
    }

    /**
     * Create and auto-submit redirect form to MyPOS
     *
     * @param string $actionUrl
     * @param array $formData
     */
    protected function _createRedirectForm($actionUrl, $formData)
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>Redirecting to MyPOS...</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .loading { font-size: 18px; color: #666; }
    </style>
</head>
<body>
    <div class="loading">Redirecting to MyPOS payment gateway...</div>
    <form id="mypos_form" method="POST" action="' . htmlspecialchars($actionUrl) . '">';

        foreach ($formData as $key => $value) {
            $html .= '<input type="hidden" name="' . htmlspecialchars($key) . '" value="' . htmlspecialchars($value) . '" />';
        }

        $html .= '
    </form>
    <script>
        document.getElementById("mypos_form").submit();
    </script>
</body>
</html>';

        $this->getResponse()->setBody($html);
    }

    /**
     * Test action for development
     */
    public function testAction()
    {
        if (!Mage::getIsDeveloperMode()) {
            $this->norouteAction();
            return;
        }

        echo "MyPOS Test Action - Module is working!";
        exit;
    }
}
