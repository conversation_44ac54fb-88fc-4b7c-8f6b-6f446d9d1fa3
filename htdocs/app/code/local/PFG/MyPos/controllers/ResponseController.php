<?php

class PFG_MyPos_ResponseController extends Mage_Core_Controller_Front_Action
{
    /**
     * Handle successful payment response
     */
    public function successAction()
    {
        try {
            $request = $this->getRequest();
            $helper = Mage::helper('pfg_mypos');
            $myposHelper = Mage::helper('pfg_mypos/mypos');
            
            // Get response data
            $responseData = $request->getParams();
            $helper->log('MyPOS success response received: ' . json_encode($responseData));
            
            // Validate required parameters
            if (!isset($responseData['ORDER']) || !isset($responseData['STATUS'])) {
                throw new Exception('Missing required parameters in MyPOS response');
            }
            
            $myposOrderId = $responseData['ORDER'];
            $status = $responseData['STATUS'];
            
            // Get order
            $order = $helper->getOrderByMyPosOrderId($myposOrderId);
            if (!$order || !$order->getId()) {
                throw new Exception('Order not found for MyPOS order ID: ' . $myposOrderId);
            }
            
            // Validate signature using new direct validation
            $this->validateResponse($responseData, $order);
            
            // Process successful payment
            if ($status == '00') { // Success status
                $this->processSuccessfulPayment($order, $responseData);
                $this->_redirect('checkout/onepage/success');
            } else {
                $this->processFailedPayment($order, $responseData);
                Mage::getSingleton('core/session')->addError($this->__('Payment was not successful.'));
                $this->_redirect('checkout/cart');
            }
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            Mage::getSingleton('core/session')->addError($this->__('An error occurred while processing payment response.'));
            $this->_redirect('checkout/cart');
        }
    }

    /**
     * Handle cancelled payment response
     */
    public function cancelAction()
    {
        try {
            $request = $this->getRequest();
            $helper = Mage::helper('pfg_mypos');
            
            $responseData = $request->getParams();
            $helper->log('MyPOS cancel response received: ' . json_encode($responseData));
            
            if (isset($responseData['ORDER'])) {
                $order = $helper->getOrderByMyPosOrderId($responseData['ORDER']);
                if ($order && $order->getId()) {
                    $this->processCancelledPayment($order, $responseData);
                }
            }
            
            Mage::getSingleton('core/session')->addNotice($this->__('Payment was cancelled.'));
            $this->_redirect('checkout/cart');
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            $this->_redirect('checkout/cart');
        }
    }

    /**
     * Handle notification from MyPOS (server-to-server)
     */
    public function notifyAction()
    {
        try {
            $request = $this->getRequest();
            $helper = Mage::helper('pfg_mypos');
            $myposHelper = Mage::helper('pfg_mypos/mypos');
            
            if (!$request->isPost()) {
                throw new Exception('Invalid request method for notification');
            }
            
            $responseData = $request->getPost();
            $helper->log('MyPOS notification received: ' . json_encode($responseData));
            
            // Validate required parameters
            if (!isset($responseData['ORDER']) || !isset($responseData['STATUS'])) {
                throw new Exception('Missing required parameters in MyPOS notification');
            }
            
            $myposOrderId = $responseData['ORDER'];
            $status = $responseData['STATUS'];
            
            // Get order
            $order = $helper->getOrderByMyPosOrderId($myposOrderId);
            if (!$order || !$order->getId()) {
                throw new Exception('Order not found for MyPOS order ID: ' . $myposOrderId);
            }
            
            // Validate signature using new direct validation
            $this->validateResponse($responseData, $order);
            
            // Process notification based on status
            if ($status == '00') {
                $this->processSuccessfulPayment($order, $responseData);
            } else {
                $this->processFailedPayment($order, $responseData);
            }
            
            // Save transaction record
            $myposHelper->saveTransaction($order, $myposOrderId, $status, $responseData);
            
            // Return success response to MyPOS
            $this->getResponse()->setBody('OK');
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            $this->getResponse()->setHttpResponseCode(500);
            $this->getResponse()->setBody('ERROR');
        }
    }

    /**
     * Process successful payment
     *
     * @param Mage_Sales_Model_Order $order
     * @param array $responseData
     */
    protected function processSuccessfulPayment(Mage_Sales_Model_Order $order, array $responseData)
    {
        $helper = Mage::helper('pfg_mypos');
        
        if ($order->getState() === Mage_Sales_Model_Order::STATE_PENDING_PAYMENT) {
            $payment = $order->getPayment();
            
            // Set transaction ID
            if (isset($responseData['TRANSACTION_ID'])) {
                $payment->setTransactionId($responseData['TRANSACTION_ID']);
            }
            
            // Set additional information
            $payment->setAdditionalInformation('mypos_order_id', $responseData['ORDER']);
            $payment->setAdditionalInformation('mypos_response', json_encode($responseData));
            
            // Create invoice if auto-invoice is enabled
            if ($order->canInvoice()) {
                $invoice = $order->prepareInvoice();
                $invoice->setRequestedCaptureCase(Mage_Sales_Model_Order_Invoice::CAPTURE_ONLINE);
                $invoice->register();
                $order->addRelatedObject($invoice);
            }
            
            // Update order status
            $newStatus = $helper->getPaymentConfig('order_status_after_payment') ?: 'processing';
            $order->setState(Mage_Sales_Model_Order::STATE_PROCESSING, $newStatus);
            $order->addStatusHistoryComment('Payment completed via MyPOS. Transaction ID: ' . (isset($responseData['TRANSACTION_ID']) ? $responseData['TRANSACTION_ID'] : 'N/A'));
            
            $order->save();
            
            $helper->log('Order ' . $order->getIncrementId() . ' payment completed successfully');
        }
    }

    /**
     * Process failed payment
     *
     * @param Mage_Sales_Model_Order $order
     * @param array $responseData
     */
    protected function processFailedPayment(Mage_Sales_Model_Order $order, array $responseData)
    {
        $helper = Mage::helper('pfg_mypos');
        
        $errorMessage = isset($responseData['ERROR_MESSAGE']) ? $responseData['ERROR_MESSAGE'] : 'Payment failed';
        
        $order->addStatusHistoryComment('MyPOS payment failed: ' . $errorMessage);
        $order->cancel();
        $order->save();
        
        $helper->log('Order ' . $order->getIncrementId() . ' payment failed: ' . $errorMessage);
    }

    /**
     * Process cancelled payment
     *
     * @param Mage_Sales_Model_Order $order
     * @param array $responseData
     */
    protected function processCancelledPayment(Mage_Sales_Model_Order $order, array $responseData)
    {
        $helper = Mage::helper('pfg_mypos');
        
        $order->addStatusHistoryComment('MyPOS payment was cancelled by customer');
        $order->cancel();
        $order->save();
        
        $helper->log('Order ' . $order->getIncrementId() . ' payment cancelled');
    }

    /**
     * Validate MyPOS response signature
     *
     * @param array $responseData
     * @param Mage_Sales_Model_Order $order
     * @throws Exception
     */
    protected function validateResponse(array $responseData, Mage_Sales_Model_Order $order)
    {
        $helper = Mage::helper('pfg_mypos');

        // Get API public key for signature validation
        $publicKey = $helper->getPaymentConfig('api_public_key', $order->getStoreId());

        if (empty($publicKey)) {
            $helper->log('Warning: API public key not configured, skipping signature validation');
            return; // Skip validation if no public key configured
        }

        // Validate signature using the new direct validation method
        if (!$helper->validateSignature($responseData, $publicKey)) {
            throw new Exception('Invalid MyPOS response signature');
        }

        $helper->log('MyPOS response signature validated successfully');
    }
}
