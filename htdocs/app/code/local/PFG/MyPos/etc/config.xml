<?xml version="1.0"?>
<config>
    <modules>
        <PFG_MyPos>
            <version>1.0.0</version>
        </PFG_MyPos>
    </modules>
    <global>
        <models>
            <pfg_mypos>
                <class>PFG_MyPos_Model</class>
                <resourceModel>pfg_mypos_resource</resourceModel>
            </pfg_mypos>
            <pfg_mypos_resource>
                <class>PFG_MyPos_Model_Resource</class>
                <entities>
                    <transaction>
                        <table>pfg_mypos_transactions</table>
                    </transaction>
                </entities>
            </pfg_mypos_resource>
        </models>
        <resources>
            <pfg_mypos_setup>
                <setup>
                    <module>PFG_MyPos</module>
                </setup>
            </pfg_mypos_setup>
        </resources>
        <blocks>
            <pfg_mypos>
                <class>PFG_MyPos_Block</class>
            </pfg_mypos>
        </blocks>
        <helpers>
            <pfg_mypos>
                <class>PFG_MyPos_Helper</class>
            </pfg_mypos>
        </helpers>
    </global>
    <frontend>
        <routers>
            <pfg_mypos>
                <use>standard</use>
                <args>
                    <module>PFG_MyPos</module>
                    <frontName>pfg_mypos</frontName>
                </args>
            </pfg_mypos>
            <theme_api>
                <use>standard</use>
                <args>
                    <modules>
                        <pfg_mypos before="Mage_Cms">PFG_MyPos</pfg_mypos>
                    </modules>
                </args>
            </theme_api>
        </routers>
        <layout>
            <updates>
                <pfg_mypos>
                    <file>pfg_mypos.xml</file>
                </pfg_mypos>
            </updates>
        </layout>
    </frontend>
    <adminhtml>
        <routers>
            <pfg_mypos>
                <use>admin</use>
                <args>
                    <module>PFG_MyPos</module>
                    <frontName>pfg_mypos</frontName>
                </args>
            </pfg_mypos>
        </routers>
    </adminhtml>
    <default>
        <payment>
            <pfg_mypos>
                <active>0</active>
                <model>pfg_mypos/payment_mypos</model>
                <title>MyPOS Card Payment</title>
                <new_order_status>pending</new_order_status>
                <order_status_after_payment>processing</order_status_after_payment>
                <sandbox_mode>1</sandbox_mode>
                <sort_order>200</sort_order>
                <allowspecific>0</allowspecific>
                <currency>BGN</currency>
                <use_direct_signing>1</use_direct_signing>
            </pfg_mypos>
        </payment>
        <pfg_mypos>
            <general>
                <sandbox_url>https://mypos.com/vmp/checkout-test/</sandbox_url>
                <production_url>https://mypos.com/vmp/checkout/</production_url>
            </general>
            <api>
                <auth_token><![CDATA[your-api-auth-token-here]]></auth_token>
            </api>
            <advanced>
                <force_log>0</force_log>
                <log_file_name><![CDATA[pfg_mypos.log]]></log_file_name>
                <exceptions_file_name><![CDATA[pfg_mypos_exceptions.log]]></exceptions_file_name>
            </advanced>
        </pfg_mypos>
    </default>
</config>
