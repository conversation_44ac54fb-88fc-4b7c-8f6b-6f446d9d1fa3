<?xml version="1.0"?>
<config>
    <sections>
        <payment>
            <groups>
                <pfg_mypos translate="label" module="pfg_mypos">
                    <label>PFG MyPOS</label>
                    <sort_order>200</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>0</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </title>
                        <sandbox_mode translate="label">
                            <label>Sandbox Mode</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sandbox_mode>
                        <merchant_id translate="label">
                            <label>Merchant ID (SID)</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Your MyPOS Store ID (SID)</comment>
                        </merchant_id>
                        <wallet_number translate="label">
                            <label>Wallet Number</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Your MyPOS Wallet Number</comment>
                        </wallet_number>
                        <key_index translate="label">
                            <label>Key Index</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>RSA Key Index (usually 1)</comment>
                        </key_index>
                        <configuration_package translate="label">
                            <label>Configuration Package</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Base64 encoded configuration package from MyPOS (alternative to manual key setup)</comment>
                        </configuration_package>
                        <private_key translate="label">
                            <label>Private Key</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Your RSA Private Key (PEM format)</comment>
                        </private_key>
                        <api_public_key translate="label">
                            <label>API Public Key</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>MyPOS API Public Key (PEM format)</comment>
                        </api_public_key>
                        <currency translate="label">
                            <label>Currency</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_currency</source_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </currency>
                        <use_direct_signing translate="label">
                            <label>Use Direct Signing</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>102</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Use improved direct signing method based on successful MyPOS integration patterns (recommended)</comment>
                        </use_direct_signing>
                        <url_ok translate="label">
                            <label>Success URL (URL_OK)</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>pfg_mypos/system_config_backend_httpsurl</backend_model>
                            <sort_order>105</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>HTTPS URL where customers are redirected after successful payment. Must start with https://</comment>
                            <validate>required-entry</validate>
                        </url_ok>
                        <url_cancel translate="label">
                            <label>Cancel URL (URL_Cancel)</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>pfg_mypos/system_config_backend_httpsurl</backend_model>
                            <sort_order>106</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>HTTPS URL where customers are redirected when payment is cancelled. Must start with https://</comment>
                            <validate>required-entry</validate>
                        </url_cancel>
                        <url_notify translate="label">
                            <label>Notification URL (URL_Notify)</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>pfg_mypos/system_config_backend_httpsurl</backend_model>
                            <sort_order>107</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>HTTPS URL for server-to-server notifications. Must start with https:// (SSL required by MyPOS)</comment>
                            <validate>required-entry</validate>
                        </url_notify>
                        <new_order_status translate="label">
                            <label>New Order Status</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_order_status</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </new_order_status>
                        <order_status_after_payment translate="label">
                            <label>Order Status After Payment</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_order_status</source_model>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </order_status_after_payment>
                        <allowspecific translate="label">
                            <label>Payment from Applicable Countries</label>
                            <frontend_type>allowspecific</frontend_type>
                            <sort_order>130</sort_order>
                            <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </allowspecific>
                        <specificcountry translate="label">
                            <label>Payment from Specific Countries</label>
                            <frontend_type>multiselect</frontend_type>
                            <sort_order>140</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </specificcountry>
                        <sort_order translate="label">
                            <label>Sort Order</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>150</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sort_order>
                    </fields>
                </pfg_mypos>
            </groups>
        </payment>
    </sections>
</config>
