<?php
/**
 * Debug script to analyze the current payload being sent
 */

// Decode the current curl payload
$curlData = 'URL_Cancel=https%3A%2F%2Fcarco.bg%2Fpfg_mypos%2Fresponse%2Fcancel&Currency_1=BGN&URL_Notify=https%3A%2F%2Fcarco.bg%2Fpfg_mypos%2Fresponse%2Fnotify&customerzipcode=1849&WalletNumber=61938166610&PaymentParametersRequired=1&customercountry=BG&PaymentMethod=3&Article_1=%D0%A1%D1%82%D1%8A%D0%BA%D0%BB%D0%BE+%D1%84%D0%B8%D0%BA%D1%81+%D0%B7%D0%B0%D0%B4%D0%B5%D0%BD+%D0%B4%D0%B5%D1%81%D0%B5%D0%BD+%D0%B7%D0%B0+AUDI+100+%D1%81%D0%B5%D0%B4%D0%B0%D0%BD+%284A%2C+C4%29+2.0+E+%281990+-+1994%29&KeyIndex=1&URL_OK=https%3A%2F%2Fcarco.bg%2Fpfg_mypos%2Fresponse%2Fsuccess&customercity=%D0%A1%D0%BE%D1%84%D0%B8%D1%8F&Amount_1=21.74&CartItems=1&Price_1=21.74&IPCVersion=1.4&ApplicationID=&expires_in=86400&customerfirstnames=Stoyan&Signature=P7yj0fc%2FZebRG%2BfFwyFpcWrLLgfyxV6Zkq3F3NthK2lTNAhyWLYq%2FOrC1iZgIYcxBCpzf9Ul7JtxD1RLdTcgr43%2BkJJcJGFf0MkU0hNHIPBvawo9pz3bXLjXkf%2F%2FMFXBR4dRDggglDoLf6KkarOvYc9TVVP%2B8bblqQ%2FOEF%2FwYIc%3D&IPCLanguage=bg&Amount=21.74&Quantity_1=1&customeremail=stoyan.atanasoff%40gmail.com&customerfamilyname=Atanasov&CardTokenRequest=0&Note=Order+%23100120970&OrderID=100120970_1753985147&IPCmethod=IPCPurchase&Currency=BGN&PartnerID=&customeraddress=%D1%83%D0%BB.+%E2%80%9E%D0%A1%D0%B2%D0%B5%D1%82%D0%B8+%D0%93%D0%B5%D0%BE%D1%80%D0%B3%D0%B8+%D0%9F%D0%BE%D0%B1%D0%B5%D0%B4%D0%BE%D0%BD%D0%BE%D1%81%D0%B5%D1%86%E2%80%9C+%E2%84%961%D0%90&Source=SDK_PHP_1.3.1&SID=000000000000010&customerphone=0883555204';

parse_str($curlData, $currentFields);

echo "<h1>Current Payload Analysis</h1>\n";

echo "<h2>Current Fields (sorted alphabetically):</h2>\n";
ksort($currentFields);
echo "<table border='1' style='border-collapse: collapse;'>\n";
echo "<tr><th>Field Name</th><th>Value</th><th>Notes</th></tr>\n";

foreach ($currentFields as $key => $value) {
    $notes = '';
    
    // Check for issues
    if ($key === 'WalletNumber') {
        $notes = '❌ Should be "walletnumber" (lowercase)';
    } elseif ($key === 'PaymentMethod') {
        $notes = '❌ Not in successful example';
    } elseif ($key === 'ApplicationID') {
        $notes = '❌ Not in successful example';
    } elseif ($key === 'expires_in') {
        $notes = '❌ Not in successful example';
    } elseif ($key === 'PartnerID') {
        $notes = '❌ Not in successful example';
    } elseif ($key === 'Source') {
        $notes = '❌ Not in successful example';
    } elseif ($key === 'Note') {
        $notes = '❌ Not in successful example';
    } elseif (in_array($key, ['IPCmethod', 'IPCVersion', 'IPCLanguage', 'SID', 'Amount', 'Currency', 'OrderID', 'URL_OK', 'URL_Cancel', 'URL_Notify', 'CardTokenRequest', 'KeyIndex', 'CartItems'])) {
        $notes = '✅ Correct field from successful example';
    } elseif (preg_match('/^(Article|Quantity|Price|Currency|Amount)_\d+$/', $key)) {
        $notes = '✅ Cart item field';
    } elseif (preg_match('/^customer/', $key)) {
        $notes = '✅ Customer field';
    }
    
    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td><td>$notes</td></tr>\n";
}
echo "</table>\n";

echo "<h2>Expected Fields from Successful Example:</h2>\n";
$expectedFields = [
    'IPCmethod' => 'IPCPurchase',
    'IPCVersion' => '1.4', 
    'IPCLanguage' => 'EN', // Note: current uses 'bg'
    'SID' => '000000000000010',
    'walletnumber' => '61938166610', // Note: current uses 'WalletNumber'
    'Amount' => '21.74',
    'Currency' => 'BGN',
    'OrderID' => '100120970_1753985147',
    'URL_OK' => 'https://carco.bg/pfg_mypos/response/success',
    'URL_Cancel' => 'https://carco.bg/pfg_mypos/response/cancel',
    'URL_Notify' => 'https://carco.bg/pfg_mypos/response/notify',
    'CardTokenRequest' => '0',
    'KeyIndex' => '1',
    'PaymentParametersRequired' => '1', // Missing in current
    'CartItems' => '1',
    'Article_1' => 'Product name',
    'Quantity_1' => '1',
    'Price_1' => '21.74',
    'Currency_1' => 'BGN',
    'Amount_1' => '21.74'
];

echo "<table border='1' style='border-collapse: collapse;'>\n";
echo "<tr><th>Expected Field</th><th>Expected Value</th><th>Current Value</th><th>Status</th></tr>\n";

foreach ($expectedFields as $field => $expectedValue) {
    $currentValue = isset($currentFields[$field]) ? $currentFields[$field] : 'MISSING';
    $status = '';
    
    if ($currentValue === 'MISSING') {
        $status = '❌ Missing';
    } elseif ($currentValue === $expectedValue || $field === 'Article_1' || $field === 'OrderID') {
        $status = '✅ OK';
    } else {
        $status = '⚠️ Different';
    }
    
    echo "<tr><td><strong>$field</strong></td><td>" . htmlspecialchars($expectedValue) . "</td><td>" . htmlspecialchars($currentValue) . "</td><td>$status</td></tr>\n";
}
echo "</table>\n";

echo "<h2>Issues Found:</h2>\n";
echo "<ul>\n";
echo "<li>❌ <strong>WalletNumber</strong> should be <strong>walletnumber</strong> (lowercase)</li>\n";
echo "<li>❌ <strong>PaymentParametersRequired</strong> field is missing</li>\n";
echo "<li>❌ Extra fields not in successful example: PaymentMethod, ApplicationID, expires_in, PartnerID, Source, Note</li>\n";
echo "<li>⚠️ <strong>IPCLanguage</strong> is 'bg' but successful example uses 'EN'</li>\n";
echo "<li>⚠️ Endpoint URL should be <strong>https://www.mypos.eu/vmp/checkout-test</strong> not mypos.com</li>\n";
echo "</ul>\n";

echo "<h2>Signature Analysis:</h2>\n";
$signature = $currentFields['Signature'];
echo "<p><strong>Current Signature:</strong><br>";
echo "<code style='word-break: break-all;'>" . htmlspecialchars($signature) . "</code></p>\n";

// Remove signature for signing test
unset($currentFields['Signature']);

// Test signing with current fields
echo "<h3>Test Signing with Current Fields:</h3>\n";

// Sort and create signing string
ksort($currentFields);
$dashString = implode('-', $currentFields);
$base64String = base64_encode($dashString);

echo "<p><strong>Dash String Length:</strong> " . strlen($dashString) . " chars</p>\n";
echo "<p><strong>Base64 String Length:</strong> " . strlen($base64String) . " chars</p>\n";

// Private key from database
$privateKey = '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

try {
    $pkeyid = openssl_pkey_get_private($privateKey);
    if ($pkeyid === false) {
        echo "<p>❌ Failed to load private key</p>\n";
    } else {
        $signResult = openssl_sign($base64String, $newSignature, $pkeyid, OPENSSL_ALGO_SHA256);
        openssl_free_key($pkeyid);
        
        if ($signResult) {
            $newSignatureBase64 = base64_encode($newSignature);
            echo "<p><strong>Calculated Signature:</strong><br>";
            echo "<code style='word-break: break-all;'>" . htmlspecialchars($newSignatureBase64) . "</code></p>\n";
            
            if ($signature === $newSignatureBase64) {
                echo "<p>✅ <strong>Signatures match!</strong> Current signing is working correctly.</p>\n";
            } else {
                echo "<p>❌ <strong>Signatures don't match!</strong> There's an issue with the signing process.</p>\n";
            }
        } else {
            echo "<p>❌ Failed to create signature</p>\n";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

?>
