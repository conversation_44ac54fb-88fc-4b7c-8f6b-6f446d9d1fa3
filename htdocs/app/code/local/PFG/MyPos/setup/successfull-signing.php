<?php
// Configuration from myPOS configuration pack
$storeId = '000000000000010';
$clientNumber = '61938166610';
$keyIndex = 1;

$privateKey = '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

// Transaction details
$totalAmount = 23.45; // Use same amount as example
$currency = 'EUR';
$orderId = uniqid('order_');

// URLs - Notice the exact field names from the example
$urlOK = 'https://yourdomain.com/paymentOK';
$urlCancel = 'https://yourdomain.com/paymentNOK';
$urlNotify = 'https://yourdomain.com/paymentNotify'; // HTTPS required for notify URL

echo "<h2>myPOS Final Working Implementation</h2>\n";

// Cart items matching the example format
$cartItems = array(
    array(
        'name' => 'Test Product',
        'quantity' => 1,
        'price' => 20.00,
        'currency' => 'EUR'
    ),
    array(
        'name' => 'Delivery',
        'quantity' => 1,
        'price' => 3.45,
        'currency' => 'EUR'
    )
);

// Calculate total
$calculatedTotal = 0;
foreach ($cartItems as $item) {
    $calculatedTotal += $item['quantity'] * $item['price'];
}

echo "<h3>Cart Summary</h3>\n";
echo "<ul>\n";
foreach ($cartItems as $index => $item) {
    echo "<li>" . htmlspecialchars($item['name']) . " - Qty: " . $item['quantity'] . " × " . number_format($item['price'], 2) . " " . $item['currency'] . " = " . number_format($item['quantity'] * $item['price'], 2) . " " . $item['currency'] . "</li>\n";
}
echo "</ul>\n";
echo "<p><strong>Total: " . number_format($calculatedTotal, 2) . " " . $currency . "</strong></p>\n";

function createMyPOSPayment($fields, $privateKey, $testName) {
    echo "<h3>$testName</h3>\n";

    // Sort fields alphabetically
    ksort($fields);

    // Debug: Show all fields
    echo "<details><summary><strong>Click to see all fields (sorted alphabetically)</strong></summary>\n";
    foreach ($fields as $key => $value) {
        echo "- $key = " . htmlspecialchars($value) . "<br>\n";
    }
    echo "</details><br>\n";

    // Concatenate with dash and Base64 encode
    $dashString = implode('-', $fields);
    $base64String = base64_encode($dashString);

    echo "<strong>Signing Details:</strong><br>\n";
    echo "String length: " . strlen($dashString) . " chars<br>\n";
    echo "Base64 length: " . strlen($base64String) . " chars<br>\n";

    // Sign with SHA-256
    $pkeyid = openssl_pkey_get_private($privateKey);
    if ($pkeyid === false) {
        echo "ERROR: Failed to load private key<br>\n";
        return;
    }

    $signResult = openssl_sign($base64String, $signature, $pkeyid, OPENSSL_ALGO_SHA256);
    openssl_free_key($pkeyid);

    if (!$signResult) {
        echo "ERROR: Failed to sign<br>\n";
        return;
    }

    $base64Signature = base64_encode($signature);
    echo "Signature: <code style='word-break: break-all;'>" . htmlspecialchars($base64Signature) . "</code><br>\n";

    // Add signature to fields
    $testFields = $fields;
    $testFields['Signature'] = $base64Signature; // Note: Capital S as in example

    $endpoint = 'https://www.mypos.eu/vmp/checkout-test';

    echo "<br><form method='POST' action='" . htmlspecialchars($endpoint) . "' target='_blank' style='margin: 15px 0;'>\n";
    foreach ($testFields as $key => $value) {
        echo "<input type='hidden' name='" . htmlspecialchars($key) . "' value='" . htmlspecialchars($value) . "' />\n";
    }
    echo "<button type='submit' style='background: #28a745; color: white; padding: 15px 25px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; font-size: 16px;'>🚀 Process Payment</button>\n";
    echo "</form>\n";

    echo "<hr>\n";
}

// Build the complete field set exactly matching the example format
$fields = array(
    // Basic payment info
    'IPCmethod' => 'IPCPurchase',
    'IPCVersion' => '1.4',
    'IPCLanguage' => 'EN',
    'SID' => $storeId,
    'walletnumber' => $clientNumber,
    'Amount' => $calculatedTotal, // Capital A as in example
    'Currency' => $currency,      // Capital C as in example
    'OrderID' => $orderId,        // Capital O, Capital I, Capital D as in example

    // URLs - exact field names from example
    'URL_OK' => $urlOK,
    'URL_Cancel' => $urlCancel,
    'URL_Notify' => $urlNotify,

    // Other required fields from example
    'CardTokenRequest' => 0,
    'KeyIndex' => $keyIndex,
    'customeremail' => '<EMAIL>',
    'customerfirstnames' => 'John',
    'customerfamilyname' => 'Smith',
    'PaymentParametersRequired' => 1,

    // Customer details (required when PaymentParametersRequired=1)
    'customeremail' => '<EMAIL>',
    'customerfirstnames' => 'John',
    'customerfamilyname' => 'Smith',
    'customerphone' => '+359888222333',
    'customercountry' => 'BGR',
    'customercity' => 'Sofia',
    'customerzipcode' => '1000',
    'customeraddress' => 'Test Street 1',

    // Cart info
    'CartItems' => count($cartItems),
);

// Add cart item fields exactly as in example
foreach ($cartItems as $index => $item) {
    $itemIndex = $index + 1; // 1-indexed
    $itemAmount = $item['quantity'] * $item['price'];

    $fields["Article_$itemIndex"] = $item['name'];
    $fields["Quantity_$itemIndex"] = $item['quantity'];
    $fields["Price_$itemIndex"] = $item['price'];
    $fields["Currency_$itemIndex"] = $item['currency']; // This was missing before!
    $fields["Amount_$itemIndex"] = $itemAmount;
}

// Test the complete payment
createMyPOSPayment($fields, $privateKey, "Complete Payment (Matching Example Format)");

// Also create a minimal version for testing
echo "<h2>Minimal Version (for testing)</h2>\n";

$minimalFields = array(
    'IPCmethod' => 'IPCPurchase',
    'IPCVersion' => '1.4',
    'IPCLanguage' => 'EN',
    'SID' => $storeId,
    'walletnumber' => $clientNumber,
    'Amount' => 10.00,
    'Currency' => $currency,
    'OrderID' => uniqid('min_'),
    'URL_OK' => $urlOK,
    'URL_Cancel' => $urlCancel,
    'URL_Notify' => $urlNotify,
    'CardTokenRequest' => 0,
    'KeyIndex' => $keyIndex,
    'CartItems' => 1,
    'Article_1' => 'Test Item',
    'Quantity_1' => 1,
    'Price_1' => 10.00,
    'Currency_1' => 'EUR',
    'Amount_1' => 10.00,
);

createMyPOSPayment($minimalFields, $privateKey, "Minimal Payment");

?>

<h2>Key Fixes Applied 🔧</h2>
<ul>
    <li>✅ <strong>Added URL_Notify field</strong> - myPOS requires this HTTPS URL for payment notifications</li>
    <li>✅ <strong>Added Currency_1 field</strong> - Each cart item needs its currency specified</li>
    <li>✅ <strong>Fixed field name capitalization</strong> - Amount, Currency, OrderID, URL_OK, etc. match example</li>
    <li>✅ <strong>Added CardTokenRequest=0</strong> - Required field from example</li>
    <li>✅ <strong>Added PaymentParametersRequired=1</strong> - Required field from example</li>
    <li>✅ <strong>Changed signature field to 'Signature'</strong> - Capital S as in example</li>
</ul>

<h2>Important Notes 📝</h2>
<ul>
    <li><strong>URL_Notify must be HTTPS</strong> - myPOS will send payment status to this URL</li>
    <li><strong>URLs should be publicly accessible</strong> - myPOS needs to reach them</li>
    <li><strong>Test both versions</strong> - Complete and Minimal</li>
    <li><strong>For production:</strong> Change endpoint to <code>https://www.mypos.eu/vmp/checkout</code></li>
</ul>

<p><strong>This should now work perfectly!</strong> 🎉</p>