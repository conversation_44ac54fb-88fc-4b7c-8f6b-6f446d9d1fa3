<?php
/**
 * Test script to verify the corrected direct signing implementation
 */

// Include Magento
require_once '/magento/app/Mage.php';
Mage::app();

echo "<h1>MyPOS Corrected Direct Signing Test</h1>\n";

// Test configuration (same as successful signing example and database)
$testConfig = array(
    'store_id' => '000000000000010',
    'wallet_number' => '61938166610',
    'key_index' => 1,
    'private_key' => '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'currency' => 'BGN',
    'sandbox_mode' => true
);

// Create corrected test fields (matching successful example exactly)
$correctedFields = array(
    'IPCmethod' => 'IPCPurchase',
    'IPCVersion' => '1.4',
    'IPCLanguage' => 'EN', // Changed from 'bg' to 'EN'
    'SID' => $testConfig['store_id'],
    'walletnumber' => $testConfig['wallet_number'], // Changed from 'WalletNumber' to 'walletnumber'
    'Amount' => 21.74,
    'Currency' => $testConfig['currency'],
    'OrderID' => '100120970_' . time(),
    'URL_OK' => 'https://carco.bg/pfg_mypos/response/success',
    'URL_Cancel' => 'https://carco.bg/pfg_mypos/response/cancel',
    'URL_Notify' => 'https://carco.bg/pfg_mypos/response/notify',
    'CardTokenRequest' => 0,
    'KeyIndex' => $testConfig['key_index'],
    'PaymentParametersRequired' => 1,
    'customeremail' => '<EMAIL>',
    'customerfirstnames' => 'Stoyan',
    'customerfamilyname' => 'Atanasov',
    'customerphone' => '0883555204',
    'customercountry' => 'BG',
    'customercity' => 'София',
    'customerzipcode' => '1849',
    'customeraddress' => 'ул. „Свети Георги Победоносец" №1А',
    'CartItems' => 1,
    'Article_1' => 'Стъкло фикс заден десен за AUDI 100 седан (4A, C4) 2.0 E (1990 - 1994)',
    'Quantity_1' => 1,
    'Price_1' => 21.74,
    'Currency_1' => 'BGN',
    'Amount_1' => 21.74,
    // NOTE: Removed extra fields that were in the curl but not in successful example:
    // PaymentMethod, ApplicationID, expires_in, PartnerID, Source, Note
);

echo "<h2>Test 1: Corrected Direct Signing Method</h2>\n";

try {
    $myposHelper = Mage::helper('pfg_mypos/mypos');
    
    // Test the corrected direct signing method
    $signature = $myposHelper->createMyPosSignature($correctedFields, $testConfig['private_key']);
    
    echo "<p><strong>✅ Success!</strong> Signature created using corrected direct method:</p>\n";
    echo "<p><code style='word-break: break-all;'>" . htmlspecialchars($signature) . "</code></p>\n";
    
    // Add signature to fields for comparison
    $correctedFields['Signature'] = $signature;
    
    echo "<h3>Corrected Fields (sorted alphabetically):</h3>\n";
    ksort($correctedFields);
    echo "<ul>\n";
    foreach ($correctedFields as $key => $value) {
        echo "<li><strong>$key:</strong> " . htmlspecialchars($value) . "</li>\n";
    }
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Test 2: Compare with Successful Example</h2>\n";

// Create minimal test like successful example
$minimalFields = array(
    'IPCmethod' => 'IPCPurchase',
    'IPCVersion' => '1.4',
    'IPCLanguage' => 'EN',
    'SID' => $testConfig['store_id'],
    'walletnumber' => $testConfig['wallet_number'],
    'Amount' => 10.00,
    'Currency' => $testConfig['currency'],
    'OrderID' => 'test_' . time(),
    'URL_OK' => 'https://carco.bg/pfg_mypos/response/success',
    'URL_Cancel' => 'https://carco.bg/pfg_mypos/response/cancel',
    'URL_Notify' => 'https://carco.bg/pfg_mypos/response/notify',
    'CardTokenRequest' => 0,
    'KeyIndex' => $testConfig['key_index'],
    'CartItems' => 1,
    'Article_1' => 'Test Item',
    'Quantity_1' => 1,
    'Price_1' => 10.00,
    'Currency_1' => 'BGN',
    'Amount_1' => 10.00,
);

// Original signing function from successful example
function createOriginalSignature($fields, $privateKey) {
    ksort($fields);
    $dashString = implode('-', $fields);
    $base64String = base64_encode($dashString);
    
    $pkeyid = openssl_pkey_get_private($privateKey);
    if ($pkeyid === false) {
        throw new Exception('Failed to load private key');
    }
    
    $signResult = openssl_sign($base64String, $signature, $pkeyid, OPENSSL_ALGO_SHA256);
    if (function_exists('openssl_free_key')) {
        openssl_free_key($pkeyid);
    }
    
    if (!$signResult) {
        throw new Exception('Failed to sign');
    }
    
    return base64_encode($signature);
}

try {
    $originalSignature = createOriginalSignature($minimalFields, $testConfig['private_key']);
    $newSignature = $myposHelper->createMyPosSignature($minimalFields, $testConfig['private_key']);
    
    echo "<p><strong>Original Method Signature:</strong><br>";
    echo "<code style='word-break: break-all;'>" . htmlspecialchars($originalSignature) . "</code></p>\n";
    
    echo "<p><strong>Corrected Direct Method Signature:</strong><br>";
    echo "<code style='word-break: break-all;'>" . htmlspecialchars($newSignature) . "</code></p>\n";
    
    if ($originalSignature === $newSignature) {
        echo "<p><strong>✅ Perfect Match!</strong> Both methods produce identical signatures.</p>\n";
    } else {
        echo "<p><strong>❌ Mismatch!</strong> Signatures are different.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error in comparison:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Test 3: Endpoint URL</h2>\n";
echo "<p><strong>Correct Endpoint (Sandbox):</strong> https://www.mypos.eu/vmp/checkout-test</p>\n";
echo "<p><strong>Correct Endpoint (Production):</strong> https://www.mypos.eu/vmp/checkout</p>\n";
echo "<p>❌ <strong>Wrong:</strong> https://www.mypos.com/vmp/checkout-test (uses .com instead of .eu)</p>\n";

echo "<h2>Summary of Fixes Applied</h2>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Field Name Fixed:</strong> 'WalletNumber' → 'walletnumber' (lowercase)</li>\n";
echo "<li>✅ <strong>Language Fixed:</strong> 'IPCLanguage' = 'EN' (not 'bg')</li>\n";
echo "<li>✅ <strong>Endpoint Fixed:</strong> Using https://www.mypos.eu/vmp/checkout-test</li>\n";
echo "<li>✅ <strong>Extra Fields Removed:</strong> PaymentMethod, ApplicationID, expires_in, PartnerID, Source, Note</li>\n";
echo "<li>✅ <strong>Logging Added:</strong> Comprehensive debug logging for troubleshooting</li>\n";
echo "<li>✅ <strong>PHP 8 Compatibility:</strong> Fixed openssl_free_key() deprecation warning</li>\n";
echo "</ul>\n";

echo "<h2>Next Steps</h2>\n";
echo "<ol>\n";
echo "<li>Enable 'Use Direct Signing' in Magento admin</li>\n";
echo "<li>Test a real payment transaction</li>\n";
echo "<li>Check the MyPOS logs for detailed signing information</li>\n";
echo "<li>Verify the signature validation on responses</li>\n";
echo "</ol>\n";

?>
