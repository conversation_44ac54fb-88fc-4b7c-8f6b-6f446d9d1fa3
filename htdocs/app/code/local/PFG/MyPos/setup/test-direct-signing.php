<?php
/**
 * Test script to verify the new direct signing implementation
 * This script tests the signing logic against the successful signing example
 */

// Include Magento
require_once dirname(__FILE__) . '/../../../../Mage.php';
Mage::app();

echo "<h1>MyPOS Direct Signing Test</h1>\n";

// Test configuration (same as successful signing example)
$testConfig = array(
    'store_id' => '000000000000010',
    'wallet_number' => '61938166610',
    'key_index' => 1,
    'private_key' => '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'currency' => 'EUR',
    'sandbox_mode' => true
);

// Create test order data (minimal version from successful example)
$testFields = array(
    'IPCmethod' => 'IPCPurchase',
    'IPCVersion' => '1.4',
    'IPCLanguage' => 'EN',
    'SID' => $testConfig['store_id'],
    'walletnumber' => $testConfig['wallet_number'],
    'Amount' => 10.00,
    'Currency' => $testConfig['currency'],
    'OrderID' => 'test_' . time(),
    'URL_OK' => 'https://yourdomain.com/paymentOK',
    'URL_Cancel' => 'https://yourdomain.com/paymentNOK',
    'URL_Notify' => 'https://yourdomain.com/paymentNotify',
    'CardTokenRequest' => 0,
    'KeyIndex' => $testConfig['key_index'],
    'CartItems' => 1,
    'Article_1' => 'Test Item',
    'Quantity_1' => 1,
    'Price_1' => 10.00,
    'Currency_1' => 'EUR',
    'Amount_1' => 10.00,
);

echo "<h2>Test 1: Direct Signing Method</h2>\n";

try {
    $myposHelper = Mage::helper('pfg_mypos/mypos');
    
    // Test the new direct signing method
    $signature = $myposHelper->createMyPosSignature($testFields, $testConfig['private_key']);
    
    echo "<p><strong>✅ Success!</strong> Signature created using direct method:</p>\n";
    echo "<p><code style='word-break: break-all;'>" . htmlspecialchars($signature) . "</code></p>\n";
    
    // Add signature to fields for comparison
    $testFields['Signature'] = $signature;
    
    echo "<h3>All Fields (sorted alphabetically):</h3>\n";
    ksort($testFields);
    echo "<ul>\n";
    foreach ($testFields as $key => $value) {
        echo "<li><strong>$key:</strong> " . htmlspecialchars($value) . "</li>\n";
    }
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Test 2: Compare with Original Successful Method</h2>\n";

// Recreate the exact signing from successful example
function createOriginalSignature($fields, $privateKey) {
    // Sort fields alphabetically
    ksort($fields);
    
    // Concatenate with dash and Base64 encode
    $dashString = implode('-', $fields);
    $base64String = base64_encode($dashString);
    
    // Sign with SHA-256
    $pkeyid = openssl_pkey_get_private($privateKey);
    if ($pkeyid === false) {
        throw new Exception('Failed to load private key');
    }
    
    $signResult = openssl_sign($base64String, $signature, $pkeyid, OPENSSL_ALGO_SHA256);
    openssl_free_key($pkeyid);
    
    if (!$signResult) {
        throw new Exception('Failed to sign');
    }
    
    return base64_encode($signature);
}

try {
    // Remove signature for clean comparison
    unset($testFields['Signature']);
    
    $originalSignature = createOriginalSignature($testFields, $testConfig['private_key']);
    $newSignature = $myposHelper->createMyPosSignature($testFields, $testConfig['private_key']);
    
    echo "<p><strong>Original Method Signature:</strong><br>";
    echo "<code style='word-break: break-all;'>" . htmlspecialchars($originalSignature) . "</code></p>\n";
    
    echo "<p><strong>New Direct Method Signature:</strong><br>";
    echo "<code style='word-break: break-all;'>" . htmlspecialchars($newSignature) . "</code></p>\n";
    
    if ($originalSignature === $newSignature) {
        echo "<p><strong>✅ Perfect Match!</strong> Both methods produce identical signatures.</p>\n";
    } else {
        echo "<p><strong>❌ Mismatch!</strong> Signatures are different.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error in comparison:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Test 3: Signature Validation</h2>\n";

// Test signature validation (we'll need a public key for this)
$testPublicKey = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCf0TdcTuphb7X+Zwekt1XKEWZDczSGecfo6vQfqvraf5VPzcnJ2Mc5J72HBm0u98EJHan+nle2WOZMVGItTa/2k1FRWwbt7iQ5dzDh5PEeZASg2UWehoR8L8MpNBqH6h7ZITwVTfRS4LsBvlEfT7Pzhm5YJKfM+CdzDM+L9WVEGwIDAQAB
-----END PUBLIC KEY-----';

try {
    // Create a response-like data structure
    $responseData = array(
        'ORDER' => $testFields['OrderID'],
        'STATUS' => '00',
        'TRANSACTION_ID' => 'test_txn_' . time(),
        'AMOUNT' => $testFields['Amount'],
        'CURRENCY' => $testFields['Currency'],
        'Signature' => $newSignature
    );
    
    // Remove signature for validation
    $dataToValidate = $responseData;
    unset($dataToValidate['Signature']);
    
    // Create signature for validation test
    $validationSignature = $myposHelper->createMyPosSignature($dataToValidate, $testConfig['private_key']);
    $responseData['Signature'] = $validationSignature;
    
    // Test validation
    $isValid = $myposHelper->validateMyPosSignature($responseData, $testPublicKey);
    
    if ($isValid) {
        echo "<p><strong>✅ Validation Success!</strong> Signature validation works correctly.</p>\n";
    } else {
        echo "<p><strong>❌ Validation Failed!</strong> Signature validation is not working.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error in validation test:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p>The new direct signing implementation has been tested against the successful signing example.</p>\n";
echo "<p>If all tests pass, the MyPOS module should now work correctly with the improved signing logic.</p>\n";

?>
